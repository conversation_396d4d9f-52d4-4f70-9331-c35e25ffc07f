import React from 'react';
import Link from 'next/link';

const StatsSection = () => {
  return (
    <section className="bg-[#1E2A36] text-white py-16 md:py-20 my-6">
      <div className="container mx-auto px-6 md:px-10">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Let&apos;s close the loop in the Nordics together<span className="text-[#FF8A00]">.</span></h2>
        <p className="text-center text-gray-300 mb-12">Turn your waste management challenges into opportunities</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white rounded-2xl p-6 text-center">
            <div className="text-2xl md:text-3xl font-bold text-[#1E2A36] mb-2">4.5m tons</div>
            <p className="text-gray-700 text-sm md:text-base">Waste generated by Swedish businesses annually</p>
          </div>

          <div className="bg-white rounded-2xl p-6 text-center">
            <div className="text-2xl md:text-3xl font-bold text-[#1E2A36] mb-2">Only 40%</div>
            <p className="text-gray-700 text-sm md:text-base">Municipal waste in Sweden is recycled</p>
          </div>

          <div className="bg-white rounded-2xl p-6 text-center">
            <div className="text-2xl md:text-3xl font-bold text-[#1E2A36] mb-2">10-30%</div>
            <p className="text-gray-700 text-sm md:text-base">Average cost savings using our marketplace</p>
          </div>
        </div>

        <div className="flex justify-center">
          <Link
            href="/coming-soon"
            className="bg-[#FF8A00] text-white px-8 py-4 rounded-md hover:bg-[#e67e00] transition-colors inline-block text-center font-medium"
          >
            Calculate your savings
          </Link>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
