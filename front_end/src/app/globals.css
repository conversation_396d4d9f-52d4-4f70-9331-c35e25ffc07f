@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

*{
  /* margin: 0;
  padding: 0; */
  box-sizing: border-box;
}
html,body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

:root {
  --primary-blue: #1E2A36;
  --accent-orange: #FF8A00;
  --white: #FFFFFF;
  --black-text: #1A1A1A;
  --gray-text: #666666;
  --light-gray: #F5F5F5;

  /* Spacing system */
  --spacing-8: 8px;
  --spacing-16: 16px;
  --spacing-24: 24px;
  --spacing-32: 32px;
  --spacing-48: 48px;
  --spacing-64: 64px;
  --spacing-80: 80px;
  --spacing-100: 100px;

  /* Shadows */
  --shadow-subtle: 0px 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0px 8px 24px rgba(0, 0, 0, 0.12);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@theme inline {
  --color-background: var(--white);
  --color-foreground: var(--black-text);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

body {
  background: var(--white);
  color: var(--black-text);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* h1 {
  font-size: 40px;
  line-height: 48px;
  letter-spacing: -0.5px;
  font-weight: 600;
}

h2 {
  font-size: 32px;
  line-height: 40px;
  font-weight: 600;
}

h3 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 600;
}

p {
  font-size: 16px;
  line-height: 24px;
} */

.small-text {
  font-size: 14px;
  line-height: 20px;
}

.btn {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 6px;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--accent-orange);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #e67e00;
}

.rounded-corners{
  @apply rounded-2xl
}

.header-h1{
  @apply text-center text-[16px]  mb-4 text-[#e67e00]
}
.header-h2{
  @apply text-center text-xl md:text-2xl font-semibold mb-3 text-gray-300
}
.text-p1{
  @apply text-center max-w-7xl mb-6 text-sm md:text-base text-gray-500
}
.sections-margin{
  @apply my-10 md:my-16;
}

.sections-padding{
  @apply p-10 md:p-16;
}

/* .section-margin{
  @apply mx-16;
  @apply my-16;
} */

/* .container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
} */

/* Section alignment system */
/* section {
  @apply py-16;

  & > div {
    @apply mx-auto px-4 max-w-7xl w-full;
  }
} */

.section {
  /* padding: var(--spacing-100) 0; */
}

.card {
  border-radius: 8px;
  padding: var(--spacing-32);
  box-shadow: var(--shadow-subtle);
}

/* Footer newsletter alignment */
.footer-newsletter-inputs {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .footer-newsletter-inputs {
    flex-direction: row;
    gap: 1rem;
    margin-left: 0;
    position: relative;
    left: -30px;
  }
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
