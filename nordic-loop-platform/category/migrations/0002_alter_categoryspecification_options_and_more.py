# Generated by Django 5.2 on 2025-06-08 10:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('category', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='categoryspecification',
            options={'verbose_name': 'Category Specification', 'verbose_name_plural': 'Category Specifications'},
        ),
        migrations.AddField(
            model_name='categoryspecification',
            name='additional_specifications',
            field=models.TextField(blank=True, help_text='e.g., Melt Flow Index: 2.5, Density: 0.95 g/cm³', null=True),
        ),
        migrations.AlterField(
            model_name='categoryspecification',
            name='Category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='specifications', to='category.category'),
        ),
        migrations.AlterField(
            model_name='categoryspecification',
            name='material_form',
            field=models.CharField(blank=True, choices=[('pellets_granules', 'Pellets/Granules'), ('flakes', 'Flakes'), ('regrind', 'Regrind'), ('sheets', 'Sheets'), ('film', 'Film'), ('parts_components', 'Parts/Components'), ('powder', 'Powder'), ('fiber', 'Fiber')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='categoryspecification',
            name='material_grade',
            field=models.CharField(blank=True, choices=[('virgin_grade', 'Virgin Grade'), ('industrial_grade', 'Industrial Grade'), ('food_grade', 'Food Grade'), ('medical_grade', 'Medical Grade'), ('automotive_grade', 'Automotive Grade'), ('electrical_grade', 'Electrical Grade'), ('recycled_grade', 'Recycled Grade')], max_length=50, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='categoryspecification',
            unique_together={('Category', 'color', 'material_grade', 'material_form')},
        ),
    ]
