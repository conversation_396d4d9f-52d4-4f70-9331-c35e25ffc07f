# Generated by Django 5.2 on 2025-06-15 20:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ads', '0006_update_material_image_to_firebase'),
        ('company', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('business', 'Business'), ('shipping', 'Shipping'), ('billing', 'Billing')], max_length=20)),
                ('address_line1', models.CharField(max_length=255)),
                ('address_line2', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.Char<PERSON>ield(max_length=100)),
                ('postal_code', models.Char<PERSON><PERSON>(max_length=20)),
                ('country', models.Char<PERSON><PERSON>(max_length=100)),
                ('is_verified', models.<PERSON>olean<PERSON>ield(default=False)),
                ('is_primary', models.BooleanField(default=False)),
                ('contact_name', models.CharField(max_length=255)),
                ('contact_phone', models.CharField(max_length=50)),
                ('created_at', models.DateField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to='company.company')),
            ],
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan', models.CharField(choices=[('basic', 'Basic'), ('premium', 'Premium'), ('enterprise', 'Enterprise')], max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('payment_failed', 'Payment Failed')], default='active', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('auto_renew', models.BooleanField(default=True)),
                ('payment_method', models.CharField(choices=[('credit_card', 'Credit Card'), ('invoice', 'Invoice'), ('paypal', 'PayPal')], max_length=20)),
                ('last_payment', models.DateField()),
                ('amount', models.CharField(max_length=50)),
                ('contact_name', models.CharField(max_length=255)),
                ('contact_email', models.EmailField(max_length=254)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='company.company')),
            ],
        ),
    ]
