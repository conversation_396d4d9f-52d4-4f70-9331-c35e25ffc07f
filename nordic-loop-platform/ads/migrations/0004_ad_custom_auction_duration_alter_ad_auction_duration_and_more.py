# Generated by Django 5.2 on 2025-06-08 11:48

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ads', '0003_alter_ad_available_quantity_alter_ad_category_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ad',
            name='custom_auction_duration',
            field=models.IntegerField(blank=True, help_text="Custom auction duration in days (used when auction_duration is set to 'Custom')", null=True, validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AlterField(
            model_name='ad',
            name='auction_duration',
            field=models.IntegerField(choices=[(1, '1 day'), (3, '3 days'), (7, '7 days'), (14, '14 days'), (30, '30 days'), (0, 'Custom')], default=7),
        ),
        migrations.AlterField(
            model_name='ad',
            name='unit_of_measurement',
            field=models.CharField(choices=[('kg', 'Kilogram'), ('tons', 'Tons'), ('tonnes', 'Tonnes'), ('lbs', 'Pounds'), ('pounds', 'Pounds'), ('pieces', 'Pieces'), ('units', 'Units'), ('bales', 'Bales'), ('containers', 'Containers'), ('m³', 'Cubic Meters'), ('cubic_meters', 'Cubic Meters'), ('liters', 'Liters'), ('gallons', 'Gallons')], default='tons', max_length=15),
        ),
    ]
