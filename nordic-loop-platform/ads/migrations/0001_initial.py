# Generated by Django 5.2 on 2025-06-04 18:52

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('category', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country', models.CharField(default='Sweden', max_length=100)),
                ('state_province', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(max_length=100)),
                ('address_line', models.CharField(blank=True, max_length=255, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.Float<PERSON>ield(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Ad',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specific_material', models.TextField(blank=True, help_text='e.g., Grade 5052 Aluminum, HDPE milk bottles, etc.', null=True)),
                ('packaging', models.CharField(blank=True, choices=[('baled', 'Baled'), ('loose', 'Loose'), ('big_bag', 'Big-bag'), ('octabin', 'Octabin'), ('roles', 'Roles'), ('container', 'Container'), ('other', 'Other')], max_length=50, null=True)),
                ('material_frequency', models.CharField(blank=True, choices=[('one_time', 'One-time'), ('weekly', 'Weekly'), ('bi_weekly', 'Bi-weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], max_length=20, null=True)),
                ('additional_specifications', models.TextField(blank=True, help_text='e.g., Melt Flow Index: 2.5, Density: 0.95 g/cm³', null=True)),
                ('origin', models.CharField(blank=True, choices=[('post_industrial', 'Post-industrial'), ('post_consumer', 'Post-consumer'), ('mix', 'Mix')], max_length=20, null=True)),
                ('contamination', models.CharField(blank=True, choices=[('clean', 'Clean'), ('slightly_contaminated', 'Slightly Contaminated'), ('heavily_contaminated', 'Heavily Contaminated')], max_length=30, null=True)),
                ('additives', models.CharField(blank=True, choices=[('uv_stabilizer', 'UV Stabilizer'), ('antioxidant', 'Antioxidant'), ('flame_retardants', 'Flame retardants'), ('chlorides', 'Chlorides'), ('no_additives', 'No additives')], max_length=30, null=True)),
                ('storage_conditions', models.CharField(blank=True, choices=[('climate_controlled', 'Climate Controlled'), ('protected_outdoor', 'Protected Outdoor'), ('unprotected_outdoor', 'Unprotected Outdoor')], max_length=30, null=True)),
                ('processing_methods', models.JSONField(blank=True, default=list, help_text='List of applicable processing methods')),
                ('pickup_available', models.BooleanField(default=False)),
                ('delivery_options', models.JSONField(blank=True, default=list, help_text='List of available delivery options')),
                ('available_quantity', models.DecimalField(decimal_places=2, help_text='Total quantity available for auction', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unit_of_measurement', models.CharField(choices=[('kg', 'Kilogram'), ('g', 'Gram'), ('lb', 'Pound'), ('tons', 'Tons')], default='tons', max_length=10)),
                ('minimum_order_quantity', models.DecimalField(decimal_places=2, default=0, help_text='Minimum quantity buyers must purchase (0 for no minimum)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))])),
                ('starting_bid_price', models.DecimalField(decimal_places=2, help_text='Initial bid price per unit', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('currency', models.CharField(choices=[('EUR', 'Euro'), ('USD', 'US Dollar'), ('SEK', 'Swedish Krona'), ('GBP', 'British Pound')], default='EUR', max_length=3)),
                ('auction_duration', models.IntegerField(choices=[(1, '1 day'), (3, '3 days'), (7, '7 days'), (14, '14 days'), (30, '30 days')], default=7)),
                ('reserve_price', models.DecimalField(blank=True, decimal_places=2, help_text='If no bids reach this price, the auction will not complete', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('keywords', models.CharField(blank=True, help_text='Keywords separated by commas', max_length=500, null=True)),
                ('material_image', models.ImageField(blank=True, null=True, upload_to='material_images/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('auction_start_date', models.DateTimeField(blank=True, null=True)),
                ('auction_end_date', models.DateTimeField(blank=True, null=True)),
                ('current_step', models.IntegerField(default=1)),
                ('is_complete', models.BooleanField(default=False)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='category.category')),
                ('specification', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='category.categoryspecification')),
                ('subcategory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='category.subcategory')),
            ],
            options={
                'verbose_name': 'Material Ad',
                'verbose_name_plural': 'Material Ads',
                'ordering': ['-created_at'],
            },
        ),
    ]
