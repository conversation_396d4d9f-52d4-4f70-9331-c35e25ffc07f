# Generated by Django 5.2 on 2025-07-19 21:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='target_subscription_tiers',
            field=models.CharField(choices=[('all', 'All Users'), ('free', 'Free Users'), ('standard', 'Standard Users'), ('premium', 'Premium Users'), ('paid', 'All Paid Users')], default='all', help_text='Target specific subscription tiers with this notification', max_length=20),
        ),
        migrations.AlterField(
            model_name='notification',
            name='type',
            field=models.CharField(choices=[('feature', 'Feature'), ('system', 'System'), ('auction', 'Auction'), ('promotion', 'Promotion'), ('welcome', 'Welcome')], default='feature', max_length=20),
        ),
    ]
