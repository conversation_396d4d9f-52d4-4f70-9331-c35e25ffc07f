# Generated by Django 5.2 on 2025-07-27 20:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0003_remove_notification_target_subscription_tiers'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='action_url',
            field=models.URLField(blank=True, help_text='Optional URL for notification action', null=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for the notification'),
        ),
        migrations.AddField(
            model_name='notification',
            name='priority',
            field=models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10),
        ),
        migrations.AlterField(
            model_name='notification',
            name='type',
            field=models.CharField(choices=[('feature', 'Feature'), ('system', 'System'), ('auction', 'Auction'), ('promotion', 'Promotion'), ('welcome', 'Welcome'), ('subscription', 'Subscription'), ('security', 'Security'), ('account', 'Account'), ('bid', 'Bid'), ('payment', 'Payment'), ('admin', 'Admin')], default='feature', max_length=20),
        ),
    ]
