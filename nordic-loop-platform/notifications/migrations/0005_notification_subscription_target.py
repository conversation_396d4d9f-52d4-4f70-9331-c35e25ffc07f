# Generated by Django 5.2 on 2025-07-27 23:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0004_notification_action_url_notification_metadata_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='subscription_target',
            field=models.CharField(choices=[('all', 'All Users'), ('free', 'Free Plan Users'), ('standard', 'Standard Plan Users'), ('premium', 'Premium Plan Users')], default='all', help_text='Target users based on their subscription plan', max_length=20),
        ),
    ]
