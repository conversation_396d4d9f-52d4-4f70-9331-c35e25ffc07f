# Generated by Django 5.2 on 2025-07-28 10:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0003_remove_user_subscription_tier"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="contact_type",
            field=models.CharField(
                choices=[
                    ("primary", "Primary Contact"),
                    ("secondary", "Secondary Contact"),
                    ("regular", "Regular User"),
                ],
                default="regular",
                help_text="Type of contact this user represents",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="is_primary_contact",
            field=models.Bo<PERSON>anField(
                default=False,
                help_text="Indicates if this user is the primary contact for their company",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="position",
            field=models.CharField(
                blank=True,
                help_text="Job position/title of the user",
                max_length=255,
                null=True,
            ),
        ),
    ]
