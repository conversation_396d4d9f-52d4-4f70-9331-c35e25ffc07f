# # from django.test import TestCase
# # from company.repository.company_repository import CompanyRepository
# # from company.models import Company
# # from users.models import CustomUser
# # from django.db import IntegrityError

# # class CompanyRepositoryTests(TestCase):
# #     def setUp(self):
# #         self.repository = CompanyRepository()
        

# #     def test_create_company(self):
    
# #         company = self.repository.create_company(
# #             vat_number="12345678",
# #             official_name="Test Company",
# #             business_address="123 Test St",
# #             phone_number="+1234567890",
# #             email="<EMAIL>"
# #         )
# #         self.assertIsInstance(company, Company)
# #         self.assertEqual(company.vat_number, "12345678")
# #         self.assertEqual(company.official_name, "Test Company")
        
# #     def test_get_company_by_vat(self):
# #         test_user = self.create_test_user(email="<EMAIL>")
# #         created_company = self.repository.create_company(
# #             vat_number="12345678",
# #             official_name="Test Company",
# #             business_address="123 Test St",
# #             phone_number="+1234567890",
# #             user=test_user,
# #             email="<EMAIL>"
# #         )
        
# #         found_company = self.repository.get_company_by_vat("12345678")
# #         self.assertEqual(created_company, found_company)
        
# #     def test_search_companies(self):
# #         # Create first company with first user
# #         user1 = self.create_test_user(email="<EMAIL>", name="User One")
# #         company1 = self.repository.create_company(
# #             vat_number="12345678",
# #             official_name="Alpha Company",
# #             business_address="123 Test St",
# #             phone_number="+1234567890",
# #             user=user1,
# #             email="<EMAIL>"
# #         )
        
# #         # Create second company with second user
# #         user2 = self.create_test_user(email="<EMAIL>", name="User Two")
# #         company2 = self.repository.create_company(
# #             vat_number="87654321",
# #             official_name="Beta Company",
# #             business_address="456 Test Ave",
# #             phone_number="+0987654321",
# #             user=user2,
# #             email="<EMAIL>"
# #         )
        
# #         results = self.repository.search_companies("Alpha")
# #         self.assertEqual(len(results), 1)
# #         self.assertEqual(results[0], company1)
        
# #     def test_update_company(self):
# #         test_user = self.create_test_user(email="<EMAIL>")
# #         company = self.repository.create_company(
# #             vat_number="12345678",
# #             official_name="Test Company",
# #             business_address="123 Test St",
# #             phone_number="+1234567890",
# #             user=test_user,
# #             email="<EMAIL>"
# #         )
        
# #         updated_company = self.repository.update_company(
# #             company,
# #             official_name="Updated Company",
# #             business_address="456 New St"
# #         )
        
# #         self.assertEqual(updated_company.official_name, "Updated Company")
# #         self.assertEqual(updated_company.business_address, "456 New St")

# #     def test_delete_company(self):
# #         test_user = self.create_test_user(email="<EMAIL>")
# #         company = self.repository.create_company(
# #             vat_number="12345678",
# #             official_name="Test Company",
# #             business_address="123 Test St",
# #             phone_number="+1234567890",
# #             user=test_user,
# #             email="<EMAIL>"
# #         )
        
# #         # Verify company exists
# #         self.assertTrue(Company.objects.filter(id=company.id).exists())
        
# #         # Delete company
# #         self.repository.delete_company(company)
        
# #         # Verify company no longer exists
# #         self.assertFalse(Company.objects.filter(id=company.id).exists())

# #     def test_duplicate_vat_number(self):
# #         # Create first company
# #         user1 = self.create_test_user(email="<EMAIL>")
# #         self.repository.create_company(
# #             vat_number="12345678",
# #             official_name="Test Company 1",
# #             business_address="123 Test St",
# #             phone_number="+1234567890",
# #             user=user1,
# #             email="<EMAIL>"
# #         )
        
       
# #         user2 = self.create_test_user(email="<EMAIL>")
# #         with self.assertRaises(IntegrityError):
# #             self.repository.create_company(
# #                 vat_number="12345678",  
# #                 official_name="Test Company 2",
# #                 business_address="456 Test Ave",
# #                 phone_number="+0987654321",
# #                 user=user2,
# #                 email="<EMAIL>"
#             )
