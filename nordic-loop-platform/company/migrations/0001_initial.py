# Generated by Django 5.2 on 2025-06-04 18:52

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('official_name', models.CharField(max_length=255)),
                ('vat_number', models.CharField(max_length=20, unique=True, validators=[django.core.validators.MinLengthValidator(8)])),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('sector', models.CharField(choices=[('manufacturing  & Production', 'Manufacturing & Production'), ('construction', 'Construction & Demolition'), ('retail', 'Wholesale & Retail'), ('packaging', 'Packaging & Printing'), ('recycling', 'Recycling & Waste Management'), ('Energy & Utilities', 'Energy & Utilities'), ('Other', 'Other')], default='manufacturing  & Production', max_length=255)),
                ('country', models.CharField(max_length=255)),
                ('website', models.URLField(default='http://example.com')),
                ('primary_first_name', models.CharField(blank=True, max_length=255, null=True)),
                ('primary_last_name', models.CharField(blank=True, max_length=255, null=True)),
                ('primary_email', models.EmailField(blank=True, max_length=254, null=True, unique=True)),
                ('primary_position', models.CharField(blank=True, max_length=255, null=True)),
                ('secondary_first_name', models.CharField(blank=True, max_length=255, null=True)),
                ('secondary_last_name', models.CharField(blank=True, max_length=255, null=True)),
                ('secondary_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('secondary_position', models.CharField(blank=True, max_length=255, null=True)),
                ('registration_date', models.DateField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected')], db_index=True, default='pending', max_length=20, verbose_name='Approval Status')),
            ],
        ),
    ]
