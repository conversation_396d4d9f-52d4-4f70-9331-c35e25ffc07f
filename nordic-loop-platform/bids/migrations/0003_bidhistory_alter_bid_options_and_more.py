# Generated by Django 5.2 on 2025-06-06 10:45

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ads', '0003_alter_ad_available_quantity_alter_ad_category_and_more'),
        ('bids', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BidHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('new_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('previous_volume', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('new_volume', models.DecimalField(decimal_places=2, max_digits=10)),
                ('change_reason', models.CharField(choices=[('bid_placed', 'Bid Placed'), ('bid_updated', 'Bid Updated'), ('auto_bid', 'Auto Bid'), ('outbid', 'Outbid')], max_length=50)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AlterModelOptions(
            name='bid',
            options={'ordering': ['-created_at'], 'verbose_name': 'Bid', 'verbose_name_plural': 'Bids'},
        ),
        migrations.RenameField(
            model_name='bid',
            old_name='timestamp',
            new_name='created_at',
        ),
        migrations.AlterUniqueTogether(
            name='bid',
            unique_together={('user', 'ad')},
        ),
        migrations.AddField(
            model_name='bid',
            name='bid_price_per_unit',
            field=models.DecimalField(decimal_places=2, default=1.3, help_text='Bid price per unit of material', max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='bid',
            name='is_auto_bid',
            field=models.BooleanField(default=False, help_text='Whether this bid was placed automatically'),
        ),
        migrations.AddField(
            model_name='bid',
            name='max_auto_bid_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum price for auto-bidding', max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
        ),
        migrations.AddField(
            model_name='bid',
            name='notes',
            field=models.TextField(blank=True, help_text='Additional notes or requirements from the bidder', null=True),
        ),
        migrations.AddField(
            model_name='bid',
            name='total_bid_value',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Automatically calculated: bid_price_per_unit * volume_requested', max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='bid',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='bid',
            name='volume_requested',
            field=models.DecimalField(decimal_places=2, default=1.3, help_text='Quantity of material requested', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='bid',
            name='volume_type',
            field=models.CharField(choices=[('partial', 'Partial Volume'), ('full', 'Full Volume')], default='partial', max_length=10),
        ),
        migrations.AlterField(
            model_name='bid',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('outbid', 'Outbid'), ('winning', 'Winning'), ('won', 'Won'), ('lost', 'Lost'), ('cancelled', 'Cancelled')], default='active', max_length=20),
        ),
        migrations.AlterField(
            model_name='bid',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bids', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='bid',
            index=models.Index(fields=['ad', '-bid_price_per_unit'], name='bids_bid_ad_id_ae59b9_idx'),
        ),
        migrations.AddIndex(
            model_name='bid',
            index=models.Index(fields=['user', '-created_at'], name='bids_bid_user_id_739900_idx'),
        ),
        migrations.AddIndex(
            model_name='bid',
            index=models.Index(fields=['status'], name='bids_bid_status_071bbe_idx'),
        ),
        migrations.AddField(
            model_name='bidhistory',
            name='bid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='bids.bid'),
        ),
        migrations.RemoveField(
            model_name='bid',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='bid',
            name='current_Highest_amount',
        ),
        migrations.RemoveField(
            model_name='bid',
            name='volume',
        ),
    ]
