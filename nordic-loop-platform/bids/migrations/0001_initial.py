# Generated by Django 5.2 on 2025-06-04 18:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('ads', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bid',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('current_Highest_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('volume', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('status', models.CharField(choices=[('Outbid', 'Outbid'), ('Highest_bid', 'Highest_bid')], default='Highest_bid', max_length=20, null=True)),
                ('timestamp', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('ad', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bids', to='ads.ad')),
            ],
        ),
    ]
